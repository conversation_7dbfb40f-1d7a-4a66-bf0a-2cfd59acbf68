/* CSS Variables for HorizontalScroll component */
:root {
    --fg: #121214;
    --bg: #f7f7f2;
    --accent1: #727072;
    --accent2: #8a8a8a;
    --accent3: #a0a0a0;
}

/* horizontal scroll section */
.horizontal-scroll-container {
    position: relative;
    width: 100vw;
    height: 300vh;
    /* 3x viewport height for smooth scrolling */
    overflow: hidden;
}

.horizontal-scroll-wrapper {
    position: sticky;
    top: 0;
    width: 300vw;
    /* 3x viewport width for 3 items */
    height: 100vh;
    display: flex;
    will-change: transform;
}

/* Services Section Styles for Horizontal Scroll */
.horizontal-scroll-wrapper .services {
    width: 100vw;
    height: calc(100vh - 4em);
    margin: 1.5em;
    padding: 1.5em;
    display: flex;
    gap: 1.5em;
    background: #f6efe3;
    border: 1px dashed var(--fg);
    border-radius: 1em;
    box-sizing: border-box;
}

.horizontal-scroll-wrapper .services-col:nth-child(1) {
    position: relative;
    flex: 2;
    border: 1px dashed var(--fg);
    border-radius: 1em;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    overflow: hidden;
}

.horizontal-scroll-wrapper .services-banner {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    background-color: #000000;
    border-radius: 0.5em;
    overflow: hidden;
}

.horizontal-scroll-wrapper .services-banner img {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 0.5em;
    z-index: 1;
}

.horizontal-scroll-wrapper .services-banner::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 0.5em;
    pointer-events: none;
    z-index: 2;
}

.horizontal-scroll-wrapper .services-col:nth-child(1) p {
    position: relative;
    color: #f9f9f9;
    z-index: 3;
    font-size: 1.5rem;
    font-weight: 600;
}

.horizontal-scroll-wrapper .services-col:nth-child(2) {
    flex: 4;
}

.horizontal-scroll-wrapper .services-col h4 {
    margin-bottom: 1em;
    font-size: 1.8rem;
    line-height: 1.2;
    color: #000000;
    font-weight: 700;
    font-style: italic;
    letter-spacing: -0.5px;
}

.horizontal-scroll-wrapper .services-list {
    display: flex;
    flex-direction: column;
    gap: 1em;
}

.horizontal-scroll-wrapper .services-list h5 {
    color: #000000;
    font-weight: 700;
    font-size: 1.1rem;
    font-style: italic;
}

.horizontal-scroll-wrapper .services-list p {
    color: #000000;
    font-size: 1rem;
    line-height: 1.5;
}

.service-buttons {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.service-btn {
    padding: 0.8rem 1.5rem;
    border: 1px solid #000000;
    border-radius: 0.3rem;
    background-color: transparent;
    color: #000000;
    font-size: 0.9rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.service-btn:hover {
    background-color: #000000;
    color: #ffffff;
}

.horizontal-scroll-wrapper .service-list-row {
    width: 100%;
    display: flex;
    gap: 1.5em;
    padding: 1.5em 0;
    border-top: 1px dashed var(--fg);
}

.horizontal-scroll-wrapper .service-list-col:nth-child(1) {
    flex: 2;
}

.horizontal-scroll-wrapper .service-list-col:nth-child(2) {
    flex: 4;
}

/* Button styling for third container */
.horizontal-scroll-wrapper .service-buttons {
    width: 100%;
    display: flex;
    gap: 1.5em;
    padding: 1.5em 0;
    border-top: 1px dashed var(--fg);
}

.horizontal-scroll-wrapper .service-buttons button {
    flex: 1;
    padding: 0.8em 1.5em;
    border: 1px dashed var(--fg);
    border-radius: 0.5em;
    background-color: transparent;
    color: var(--fg);
    font-family: 'Rader', sans-serif;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.horizontal-scroll-wrapper .service-buttons button:hover {
    background-color: var(--fg);
    color: var(--bg);
}

/* Mobile Responsive Styles for Horizontal Scroll Services */
@media (max-width: 1000px) {
    .horizontal-scroll-wrapper .services {
        flex-direction: column;
        margin: 1em;
        padding: 1em;
    }

    .horizontal-scroll-wrapper .services-col:nth-child(1) {
        aspect-ratio: 5/3;
    }

    /* Hide images for first two sections on mobile */
    .horizontal-scroll-wrapper .services:nth-child(1) .services-col:nth-child(1),
    .horizontal-scroll-wrapper .services:nth-child(2) .services-col:nth-child(1) {
        display: none;
    }

    /* Mobile responsive h4 styling */
    .horizontal-scroll-wrapper .services-col h4 {
        margin-bottom: 0.8em;
        font-size: 1.8rem;
        line-height: 1.2;
    }

    /* Mobile responsive button styling */
    .service-buttons {
        margin-top: 0.8rem;
        gap: 0.8rem;
    }

    .service-btn {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }

    .horizontal-scroll-wrapper .service-list-row {
        flex-direction: column;
        gap: 0.8em;
    }

    .horizontal-scroll-wrapper .service-buttons {
        flex-direction: column;
        gap: 0.8em;
        margin-top: 2rem;
    }

    .horizontal-scroll-wrapper .service-buttons button {
        padding: 1.2em;
        font-size: 0.8rem;
    }
}

/* Tablet responsive styles (768px and below) */
@media (max-width: 768px) {
    .horizontal-scroll-wrapper .services {
        margin: 0.8em;
        padding: 0.8em;
        gap: 1em;
    }

    .horizontal-scroll-wrapper .services-col h4 {
        font-size: 1.5rem;
        margin-bottom: 0.6em;
    }

    .service-buttons {
        gap: 0.6rem;
        margin-top: 0.6rem;
    }

    .service-btn {
        padding: 0.5rem 0.8rem;
        font-size: 0.75rem;
    }

    .horizontal-scroll-wrapper .service-buttons {
        gap: 0.6em;
        margin-top: 1.5rem;
    }

    .horizontal-scroll-wrapper .service-buttons button {
        padding: 1em;
        font-size: 0.75rem;
    }
}

/* Small mobile responsive styles (480px and below) */
@media (max-width: 480px) {
    .horizontal-scroll-wrapper .services {
        margin: 0.5em;
        padding: 0.6em;
        gap: 0.8em;
    }

    .horizontal-scroll-wrapper .services-col h4 {
        font-size: 1.6rem;
        margin-bottom: 0.5em;
    }

    .horizontal-scroll-wrapper .services-list h5 {
        font-size: 1.4rem;
    }

    .horizontal-scroll-wrapper .services-list p {
        font-size: 1.1rem;
    }


    .service-buttons {
        gap: 0.4rem;
        margin-top: 0.5rem;
        flex-wrap: wrap;
    }

    .service-btn {
        padding: 0.4rem 0.6rem;
        font-size: 0.7rem;
        flex: 1;
        min-width: calc(50% - 0.2rem);
    }

    .horizontal-scroll-wrapper .service-buttons {
        gap: 0.5em;
        margin-top: 1rem;
    }

    .horizontal-scroll-wrapper .service-buttons button {
        padding: 0.8em;
        font-size: 0.7rem;
    }
}

/* Very small mobile responsive styles (390px and below - iPhone 12 Pro) */
@media (max-width: 390px) {
    .horizontal-scroll-wrapper .services {
        margin: 0.3em;
        padding: 0.5em;
        gap: 0.6em;
    }

    .horizontal-scroll-wrapper .services-col h4 {
        font-size: 1.1rem;
        margin-bottom: 0.4em;
    }

    .horizontal-scroll-wrapper .services-list h5 {
        font-size: 1rem;
    }

    .horizontal-scroll-wrapper .services-list p {
        font-size: 0.9rem;
    }

    .service-buttons {
        gap: 0.3rem;
        margin-top: 0.4rem;
    }

    .service-btn {
        padding: 0.3rem 0.5rem;
        font-size: 0.65rem;
        min-width: calc(50% - 0.15rem);
    }

    .horizontal-scroll-wrapper .service-buttons {
        gap: 0.4em;
        margin-top: 0.8rem;
    }

    .horizontal-scroll-wrapper .service-buttons button {
        padding: 0.7em;
        font-size: 0.65rem;
    }
}