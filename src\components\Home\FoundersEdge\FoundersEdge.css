/* Founder's Edge Component Styles */

.founders-edge-section {
  position: relative;
  width: 100vw;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-image: linear-gradient(transparent, transparent);
  color: #121214;
  padding: 2em;
}

.founders-edge-container {
  border: dashed 1px #121214;
  width: 100%;
  min-height: 150vh;
  display: flex;
  flex-direction: column;
  position: relative;
  border-radius: 50px;
  padding: 2rem;
}

.founders-edge-header {
  position: sticky;
  top: 10vh;
  left: 0;
  width: 100%;
  text-align: left;
  margin-bottom: 0rem;
  z-index: 10;
  padding-left: 1rem;
}

.founders-edge-header h1 {
  font-family: 'Rader', sans-serif;
  font-size: 2rem;
  font-weight: 700;
  color: #121214;
  letter-spacing: 1px;
  margin: 0;
  font-style: italic;
}

/* Second Header for AIR-HS Team */
.team-edge-header {
  position: sticky;
  top: 10vh;
  left: 0;
  width: 100%;
  text-align: left;
  margin-bottom: 0rem;
  margin-top: 2rem;
  z-index: 10;
  padding-left: 2rem;
}

.team-edge-header h1 {
  font-family: 'Rader', sans-serif;
  font-size: 2rem;
  font-weight: 700;
  color: var(--night);
  letter-spacing: 2px;
  margin: 0;
  font-style: italic;
}

/* Names Layout Styles - centered within dashed container */
.founders-names-layout {
  display: flex;
  flex-direction: column;
  gap: 0rem;
  width: 100%;
  min-height: 70vh;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  z-index: 2;

  padding-top: 1rem;
}

/* Team Layout Styles - centered within dashed container */
.team-names-layout {
  display: flex;
  flex-direction: column;
  gap: 0rem;
  width: 100%;
  min-height: 60vh;
  align-items: center;
  justify-content: flex-start;
  position: relative;
  z-index: 2;
  padding-top: 2rem;
}

/* Container for the large, split-letter names - exact HTML styles */
.member-name-lg {
  /* Responsive font size */
  font-size: 6vw;
  font-weight: 500;
  line-height: 1;
  display: none;
  /* Hidden on mobile by default */
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  font-family: 'Rader', sans-serif;
}

@media (min-width: 768px) {
  .member-name-lg {
    display: flex;
    /* Visible on tablets and desktops */
  }
}

/* 
@media (min-width: 1200px) {
  .member-name-lg {
    font-size: 7rem;
  }
} */

/* Style for the "DR." title - exact HTML styles */
.dr-title {
  font-size: 4vw;
  /* Smaller than the name */
  font-weight: 700;
  margin-right: 1rem;
  align-self: center;
  /* Aligns to the center of the container */
  color: #1112149a;
  font-family: 'Rader', sans-serif;
}

@media (min-width: 1200px) {
  .dr-title {
    font-size: 4rem;
  }
}

/* Each letter is a 'char' that can be animated */
.char {
  display: inline-block;
  position: relative;
}

/* The container for each team member card - exact HTML styles */
.member-card {
  cursor: pointer;
  position: relative;
  overflow: hidden;
  /* Important for containing the animations */
  padding: 1rem 0;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* Small name visible on mobile with character spans */
.member-name-sm {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  align-items: center;
  gap: 0.3rem;
  font-size: 1.8rem;
  font-weight: 600;
  text-align: center;
  font-family: 'Rader', sans-serif;
  color: #121214;
  margin: 1rem 0;
  letter-spacing: 1px;
  text-transform: uppercase;
}

/* Mobile title and name spans */
.mobile-title,
.mobile-name {
  display: inline-flex;
  gap: 0.1rem;
}

.mobile-title {
  color: #1112149a;
  font-size: 0.8em;
  font-weight: 700;
}

.mobile-name {
  color: #121214;
  font-weight: 900;
}

@media (min-width: 768px) {
  .member-name-sm {
    display: none;

  }
}

/* Name parts styling */
.name-part-1,
.name-part-2 {
  font-family: 'Rader', sans-serif;
  font-size: inherit;
  font-weight: 900;
  color: #12121484;
  letter-spacing: 0em;
  line-height: 1;
  text-transform: uppercase;
}

/* Image container styles */
.member-image-container {
  width: 120px;
  height: 200px;
  position: relative;
  /* Spacing between letters and image */
  flex-shrink: 0;
  /* Prevents image from shrinking */
}

/* Both default and hover images are absolutely positioned to stack them */
.member-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: opacity;
}

/* The hover image is initially hidden */
.member-image.hover {
  opacity: 0;
}

/* On hover of the card, the hover image becomes visible */
.member-card:hover .member-image.hover {
  opacity: 1;
}

/* And the default image hides */
.member-card:hover .member-image.default {
  opacity: 0;
}

/* Responsive adjustments for mobile */
@media (max-width: 768px) {
  .founders-edge-section {
    padding: 1em;
  }

  .founders-edge-container {
    height: auto;
    min-height: 120vh;
    border-radius: 25px;
    padding: 1rem;
    flex-direction: column;
  }

  .founders-edge-header {
    position: static;
    text-align: center;
    margin-bottom: 2rem;
  }

  .founders-edge-header h1 {
    font-size: 2.5rem;
  }

  .team-edge-header {
    position: static;
    text-align: center;
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  .team-edge-header h1 {
    font-size: 2.5rem;
  }

  .founders-names-layout,
  .team-names-layout {
    gap: 1rem;
    height: auto;
  }

  /* Mobile character spans responsive */
  .member-name-sm {
    font-size: 1.5rem;
    gap: 0.2rem;
  }

  .mobile-title {
    font-size: 0.7em;
  }

  .mobile-name {
    font-weight: 800;
  }
}

/* Small mobile responsive styles (480px and below) */
@media (max-width: 480px) {
  .founders-edge-section {
    padding: 0.5em;
  }

  .founders-edge-container {
    border-radius: 20px;
    padding: 0.8rem;
    min-height: 100vh;
  }

  .founders-edge-header h1,
  .team-edge-header h1 {
    font-size: 2rem;
  }

  .member-name-sm {
    font-size: 1.3rem;
    gap: 0.15rem;
    margin: 0.8rem 0;
  }

  .mobile-title {
    font-size: 0.6em;
  }

  .founders-names-layout,
  .team-names-layout {
    gap: 0.8rem;
    padding-top: 1rem;
  }
}

/* Very small mobile responsive styles (390px and below - iPhone 12 Pro) */
@media (max-width: 390px) {
  .founders-edge-section {
    padding: 0.3em;
  }

  .founders-edge-container {
    border-radius: 15px;
    padding: 0.6rem;
    min-height: 90vh;
  }

  .founders-edge-header h1,
  .team-edge-header h1 {
    font-size: 1.8rem;
    letter-spacing: 0.5px;
  }

  .member-name-sm {
    font-size: 1.1rem;
    gap: 0.1rem;
    margin: 0.6rem 0;
    letter-spacing: 0.5px;
  }

  .mobile-title {
    font-size: 0.5em;
  }

  .founders-names-layout,
  .team-names-layout {
    gap: 0.6rem;
    padding-top: 0.8rem;
  }
}

.image-placeholder {
  width: 120px;
  height: 160px;
  background-color: #e0e0e0;
  border: 2px dashed #727072;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0;
}

.image-placeholder::after {
  content: "IMG";
  font-family: 'Rader', sans-serif;
  font-size: 1rem;
  font-weight: 600;
  color: #727072;
  letter-spacing: 1px;
}

.founders-edge-subtitle {
  font-family: 'Rader', sans-serif;
  font-size: 1.5rem;
  font-weight: 400;
  color: #727072;
  margin: 0;
  font-style: italic;
}

.founders-edge-content {
  display: flex;
  flex-direction: column;
  gap: 4rem;
  width: 100%;
}

/* Founders Story Section */
.founders-story {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4rem;
  align-items: center;
}

.story-text h2 {
  font-family: 'Rader', sans-serif;
  font-size: 2.5rem;
  font-weight: 600;
  color: #f7f7f2;
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.story-text p {
  font-family: 'Rader', sans-serif;
  font-size: 1.2rem;
  font-weight: 400;
  color: #727072;
  line-height: 1.6;
  margin: 0;
}

.founders-stats {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 1.5rem;
  border: 1px solid #727072;
  border-radius: 20px;
  background: rgba(18, 18, 20, 0.2);
  backdrop-filter: blur(5px);
  transition: transform 0.3s ease, border-color 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  border-color: #f7f7f2;
}

.stat-number {
  font-family: 'Rader', sans-serif;
  font-size: 3rem;
  font-weight: 700;
  color: #f7f7f2;
  margin-bottom: 0.5rem;
}

.stat-label {
  font-family: 'Rader', sans-serif;
  font-size: 0.9rem;
  font-weight: 400;
  color: #727072;
  text-align: center;
  line-height: 1.3;
}

/* Founders Profiles Section */
.founders-profiles {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 4rem;
  align-items: start;
}

.founders-selector {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.founder-card {
  display: flex;
  align-items: center;
  gap: 1.5rem;
  padding: 1.5rem;
  border: 1px solid #727072;
  border-radius: 20px;
  background: rgba(18, 18, 20, 0.2);
  backdrop-filter: blur(5px);
  cursor: pointer;
  transition: all 0.3s ease;
}

.founder-card:hover,
.founder-card.active {
  border-color: #f7f7f2;
  background: rgba(247, 247, 242, 0.05);
  transform: translateX(10px);
}

.founder-card .founder-icon {
  width: 60px;
  height: 60px;
  border-radius: 15px;
  border: 2px solid #f7f7f2;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(18, 18, 20, 0.3);
  backdrop-filter: blur(5px);
  flex-shrink: 0;
}

.founder-card .founder-icon .icon {
  font-size: 2rem;
}

.founder-basic-info {
  text-align: left;
}

.founder-basic-info h3 {
  font-family: 'Rader', sans-serif;
  font-size: 1.3rem;
  font-weight: 600;
  color: #f7f7f2;
  margin: 0 0 0.5rem 0;
}

.founder-basic-info p {
  font-family: 'Rader', sans-serif;
  font-size: 0.9rem;
  font-weight: 400;
  color: #727072;
  margin: 0;
  line-height: 1.3;
}

.founder-details {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.founder-quote {
  padding: 2rem;
  border-left: 4px solid #f7f7f2;
  background: rgba(18, 18, 20, 0.2);
  border-radius: 0 20px 20px 0;
}

.founder-quote blockquote {
  font-family: 'Rader', sans-serif;
  font-size: 1.4rem;
  font-weight: 400;
  font-style: italic;
  color: #f7f7f2;
  margin: 0 0 1rem 0;
  line-height: 1.4;
}

.founder-quote cite {
  font-family: 'Rader', sans-serif;
  font-size: 1rem;
  font-weight: 500;
  color: #727072;
  font-style: normal;
}

.founder-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.5rem;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.info-label {
  font-family: 'Rader', sans-serif;
  font-size: 0.8rem;
  font-weight: 500;
  color: #727072;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.info-value {
  font-family: 'Rader', sans-serif;
  font-size: 1.1rem;
  font-weight: 600;
  color: #f7f7f2;
}

.achievements h4 {
  font-family: 'Rader', sans-serif;
  font-size: 1.2rem;
  font-weight: 600;
  color: #f7f7f2;
  margin: 0 0 1rem 0;
}

.achievements ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.achievements li {
  font-family: 'Rader', sans-serif;
  font-size: 1rem;
  font-weight: 400;
  color: #727072;
  margin-bottom: 0.8rem;
  padding-left: 1.5rem;
  position: relative;
  line-height: 1.4;
}

.achievements li::before {
  content: "→";
  position: absolute;
  left: 0;
  color: #f7f7f2;
  font-weight: 600;
}