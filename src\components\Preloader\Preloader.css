.preloader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  z-index: 1000;
  background-color: #0d1117;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preloader-content {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.preloader-header {
  position: relative;
  z-index: 2;
  color: #666565;
  display: flex;
  align-items: center;
  justify-content: center;
  height: auto;
}

.title-container {
  position: relative;
  display: inline-block;
}

.preloader-title {
  font-size: 180px;
  line-height: 1.1;
  margin: 40px;
  color: #828fd7;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0px;
}

.text-part {
  display: inline-block;
  will-change: transform;
  position: relative;
  white-space: nowrap;
}

/* Responsive adjustments */
@media screen and (max-width: 768px) {
  .preloader-content {
    padding: 1rem;
  }

  .preloader-title {
    font-size: 100px;
    gap: 0px;
    margin: 20px;
  }
}

@media screen and (max-width: 480px) {
  .preloader-content {
    padding: 0.5rem;
  }

  .preloader-title {
    font-size: 70px;
    margin: 15px;
  }
}

@media screen and (max-width: 320px) {
  .preloader-title {
    font-size: 50px;
    margin: 10px;
  }
}